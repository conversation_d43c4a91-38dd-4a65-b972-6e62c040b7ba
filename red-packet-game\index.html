<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8">
    <link rel="icon" href="/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>接红包小游戏</title>
    <script>
        !function (e, t) {
            var n = t.documentElement
                , i = e.devicePixelRatio || 1
                , a = 7.5
                , r = 16;

            n.style.setProperty("--theme-max-width", "100%");
            let maxWidth = 499;
            if (!/Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent)) {
                n.style.setProperty("width", maxWidth + "px");
                n.style.setProperty("max-width", maxWidth + "px");
                n.style.setProperty("margin", "0 auto");
                n.style.setProperty("--theme-max-width", maxWidth + "px");
            }

            function d() {
                let clientWidth = /Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent) ? n.clientWidth : maxWidth;
                var e = clientWidth / a;
                n.style.fontSize = e + "px"
            }

            if (i >= 2) {
                var o = t.createElement("body")
                    , c = t.createElement("div");
                c.style.border = ".5px solid transparent",
                    o.appendChild(c),
                    n.appendChild(o),
                1 === c.offsetHeight && n.classList.add("hairlines"),
                    n.removeChild(o)
            }
            !function e() {
                if (t.body) {
                    let clientWidth = /Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent) ? n.clientWidth : maxWidth;
                    var i = clientWidth / 100 / (a / 2);
                    t.body.style.fontSize = r * i + "px"
                } else
                    t.addEventListener("DOMContentLoaded", e)
            }(),
                d(),
                e.addEventListener("resize", d),
                e.addEventListener("pageshow", function (t) {
                    var n = !1;
                    try {
                        if (e.performance) {
                            const t = e.performance.getEntriesByType("navigation")
                                , i = !!t[0] && "back_forward" === t[0].type
                                , a = !!e.performance.navigation && 2 === e.performance.navigation.type;
                            n = i || a
                        }
                    } catch (e) {
                    }
                    (t.persisted || n) && d()
                })
        }(window, document);

        !(function () {
            var e = 0.01 * window.innerHeight,
                t = document.documentElement,
                n = e + "px";
            t.style.getPropertyValue("--vh") !== n &&
            t.style.setProperty("--vh", n);
        })();
</script>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
