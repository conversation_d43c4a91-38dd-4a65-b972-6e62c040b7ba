<template>
  <div class="game-container">
    <div class="game-area" ref="gameArea">
      <!-- 精灵角色 -->
      <div
        :class="['player', { dragging: dragState.isDragging }]"
        ref="player"
        :style="{ left: playerPosition + 'rem' }"
        @mousedown="handleMouseDown"
        @touchstart="handleTouchStart"
        @selectstart.prevent
        @dragstart.prevent
      >
        <img src="/img/redGame/sprite.png" alt="" />
      </div>

      <!-- 红包 -->
      <div
        v-for="packet in redPackets"
        :key="packet.id"
        class="red-packet"
        :style="{
          left: packet.x + 'rem',
          top: packet.y + 'rem',
          animationDuration: packet.fallDuration + 'ms'
        }"
      >
        🧧
      </div>

      <!-- 游戏说明 -->
      <div v-if="!gameRunning && !gameOver" class="instructions">
        <p>PC端：使用 ← → 方向键或 A D 键控制精灵移动</p>
        <p>移动端：直接拖拽精灵移动</p>
        <p>接住红包得分，错过红包游戏结束</p>
        <button @click="startGame" @touchstart.prevent="startGame">开始游戏</button>
      </div>

      <!-- 游戏结束界面 -->
      <div v-if="gameOver" class="game-over">
        <h2>游戏结束</h2>
        <p>最终得分: {{ score }}</p>
        <button @click="restartGame" @touchstart.prevent="restartGame">重新开始</button>
      </div>

      <!-- 分数显示 -->
      <div v-if="gameRunning && !isCountingDown" class="score">
        得分: {{ score }}
      </div>

      <!-- 倒计时显示 -->
      <div v-if="!isCountingDown" class="countdown">
        <h2>{{ gameStartCountdown }}</h2>
        <p>红包即将开始下落！</p>
      </div>

      <!-- 收集效果 -->
      <div
        v-for="effect in collectEffects"
        :key="effect.id"
        class="collect-effect"
        :style="{ left: effect.x + 'rem', top: effect.y + 'rem' }"
      >
        +{{ effect.points }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RedPacketGame',
  data() {
    return {
      gameRunning: false,
      gameOver: false,
      score: 0,
      playerPosition: 0,
      redPackets: [],
      collectEffects: [],

      // 游戏配置
      gameConfig: {
        gameWidth: 0,
        gameHeight: 0,
        playerWidth: 1.6, // rem单位
        playerHeight: 1.6, // rem单位
        gameSpeed: 1,
        remToPx: 100 // 1rem = 100px的基准值
      },

      // 控制状态
      keys: {
        left: false,
        right: false
      },

      // 拖拽状态
      dragState: {
        isDragging: false,
        dragOffset: 0,
        gameAreaRect: null,
        lastUpdateTime: 0
      },

      // 游戏循环ID
      gameLoopId: null,
      spawnTimeoutId: null,
      packetIdCounter: 0,
      effectIdCounter: 0,

      // 游戏状态
      gameStartCountdown: 0,
      isCountingDown: false,
      initialPacketsGenerated: false
    }
  },

  methods: {
    // 初始化rem适配
    initRemAdaptation() {
      const setRem = () => {
        const html = document.documentElement
        const width = html.clientWidth
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent)

        console.log('setRem called, width:', width, 'isMobile:', isMobile)

        if (isMobile) {
          // 移动端：以750px设计稿为基准，保持原有的7.5倍关系
          const fontSize = width / 7.5
          html.style.fontSize = fontSize + 'px'
          this.gameConfig.remToPx = fontSize
          console.log('Mobile fontSize:', fontSize)
        } else {
          // PC端：固定最大宽度
          const maxWidth = 499
          html.style.setProperty('width', maxWidth + 'px')
          html.style.setProperty('max-width', maxWidth + 'px')
          html.style.setProperty('margin', '0 auto')
          const fontSize = maxWidth / 7.5
          html.style.fontSize = fontSize + 'px'
          this.gameConfig.remToPx = fontSize
          console.log('PC fontSize:', fontSize)
        }
      }

      setRem()
      window.addEventListener('resize', setRem)
      window.addEventListener('pageshow', setRem)
    },

    // 初始化游戏
    async initGame() {
      await this.$nextTick()

      // 等待一小段时间确保rem适配完成
      setTimeout(() => {
        console.log('initGame called')
        console.log('gameArea ref:', this.$refs.gameArea)
        console.log('remToPx:', this.gameConfig.remToPx)

        if (this.$refs.gameArea && this.gameConfig.remToPx > 0) {
          // 获取游戏区域尺寸（转换为rem）
          const rect = this.$refs.gameArea.getBoundingClientRect()
          console.log('gameArea rect:', rect)

          this.gameConfig.gameWidth = rect.width / this.gameConfig.remToPx
          this.gameConfig.gameHeight = rect.height / this.gameConfig.remToPx
          this.playerPosition = this.gameConfig.gameWidth / 2 - this.gameConfig.playerWidth / 2

          console.log('Calculated game size (rem):', this.gameConfig.gameWidth, this.gameConfig.gameHeight)
          console.log('Player position:', this.playerPosition)

          // 设置精灵样式
          if (this.$refs.player) {
            this.$refs.player.style.cursor = 'grab'
            this.$refs.player.style.userSelect = 'none'
          }
        } else {
          console.error('gameArea ref not found or remToPx not set!')
        }
      }, 100)
    },

    // 键盘事件处理
    handleKeyDown(e) {
      switch(e.code) {
        case 'ArrowLeft':
        case 'KeyA':
          this.keys.left = true
          e.preventDefault()
          break
        case 'ArrowRight':
        case 'KeyD':
          this.keys.right = true
          e.preventDefault()
          break
      }
    },

    handleKeyUp(e) {
      switch(e.code) {
        case 'ArrowLeft':
        case 'KeyA':
          this.keys.left = false
          break
        case 'ArrowRight':
        case 'KeyD':
          this.keys.right = false
          break
      }
    },

    // 精灵触摸开始事件（类似鼠标拖拽）
    handleTouchStart(e) {
      e.preventDefault()
      e.stopPropagation() // 防止事件冒泡

      if (!e.touches || e.touches.length === 0) {
        return
      }

      this.dragState.isDragging = true

      const touch = e.touches[0]
      const rect = this.$refs.gameArea.getBoundingClientRect()
      const touchX = (touch.clientX - rect.left) / this.gameConfig.remToPx

      // 计算触摸点相对于精灵中心的偏移
      this.dragState.dragOffset = touchX - (this.playerPosition + this.gameConfig.playerWidth / 2)

      // 缓存游戏区域的边界信息，避免重复计算
      this.dragState.gameAreaRect = rect
    },

    // 触摸移动事件
    handleTouchMove(e) {
      if (!this.dragState.isDragging) return

      e.preventDefault()
      e.stopPropagation()

      if (!e.touches || e.touches.length === 0) {
        return
      }

      const touch = e.touches[0]
      // 使用缓存的边界信息，避免重复计算getBoundingClientRect
      const rect = this.dragState.gameAreaRect || this.$refs.gameArea.getBoundingClientRect()
      const touchX = (touch.clientX - rect.left) / this.gameConfig.remToPx

      // 计算新的精灵位置（考虑拖拽偏移）
      let newPosition = touchX - this.dragState.dragOffset - this.gameConfig.playerWidth / 2

      // 限制精灵在游戏区域内
      const minPos = 0
      const maxPos = this.gameConfig.gameWidth - this.gameConfig.playerWidth
      newPosition = Math.max(minPos, Math.min(newPosition, maxPos))

      // 使用节流优化，避免过于频繁的更新
      this.updatePlayerPositionThrottled(newPosition)
    },

    // 触摸结束事件
    handleTouchEnd() {
      if (!this.dragState.isDragging) return

      this.dragState.isDragging = false
      this.dragState.gameAreaRect = null // 清理缓存
    },

    // 优化的位置更新方法（节流）
    updatePlayerPositionThrottled(newPosition) {
      const now = performance.now()
      const timeDiff = now - this.dragState.lastUpdateTime

      // 只有位置真正改变且距离上次更新超过8ms时才更新（约120fps）
      if (Math.abs(this.playerPosition - newPosition) > 0.001 && timeDiff >= 8) {
        this.playerPosition = newPosition
        this.dragState.lastUpdateTime = now
      } else if (Math.abs(this.playerPosition - newPosition) > 0.01) {
        // 如果位置变化较大，立即更新
        this.playerPosition = newPosition
        this.dragState.lastUpdateTime = now
      }
    },

    // 鼠标拖拽事件处理
    handleMouseDown(e) {
      e.preventDefault()
      e.stopPropagation()
      this.dragState.isDragging = true

      const rect = this.$refs.gameArea.getBoundingClientRect()
      const mouseX = (e.clientX - rect.left) / this.gameConfig.remToPx

      // 计算鼠标点击位置相对于精灵中心的偏移
      this.dragState.dragOffset = mouseX - (this.playerPosition + this.gameConfig.playerWidth / 2)

      // 缓存游戏区域的边界信息
      this.dragState.gameAreaRect = rect

      document.body.style.userSelect = 'none'
    },

    handleMouseMove(e) {
      if (!this.dragState.isDragging) return

      e.preventDefault()
      e.stopPropagation()

      // 使用缓存的边界信息
      const rect = this.dragState.gameAreaRect || this.$refs.gameArea.getBoundingClientRect()
      const mouseX = (e.clientX - rect.left) / this.gameConfig.remToPx

      // 计算新的精灵位置（考虑拖拽偏移）
      let newPosition = mouseX - this.dragState.dragOffset - this.gameConfig.playerWidth / 2

      // 限制精灵在游戏区域内
      const minPos = 0
      const maxPos = this.gameConfig.gameWidth - this.gameConfig.playerWidth
      newPosition = Math.max(minPos, Math.min(newPosition, maxPos))

      // 使用节流优化，避免过于频繁的更新
      this.updatePlayerPositionThrottled(newPosition)
    },

    handleMouseUp() {
      if (!this.dragState.isDragging) return

      this.dragState.isDragging = false
      this.dragState.gameAreaRect = null // 清理缓存
      document.body.style.userSelect = ''
    },

    // 开始游戏
    startGame() {
      console.log('startGame called')
      console.log('gameConfig:', this.gameConfig)
      console.log('gameArea size:', this.gameConfig.gameWidth, this.gameConfig.gameHeight)

      // 重新计算游戏区域尺寸，确保准确
      if (this.$refs.gameArea && this.gameConfig.remToPx > 0) {
        const rect = this.$refs.gameArea.getBoundingClientRect()
        this.gameConfig.gameWidth = rect.width / this.gameConfig.remToPx
        this.gameConfig.gameHeight = rect.height / this.gameConfig.remToPx
        this.playerPosition = this.gameConfig.gameWidth / 2 - this.gameConfig.playerWidth / 2

        console.log('Updated game size (rem):', this.gameConfig.gameWidth, this.gameConfig.gameHeight)
        console.log('Updated player position:', this.playerPosition)
      }

      this.gameRunning = true
      this.gameOver = false
      this.score = 0
      this.gameConfig.gameSpeed = 1
      this.redPackets.splice(0)
      this.collectEffects.splice(0)
      this.initialPacketsGenerated = false

      // 生成初始红包并开始倒计时
      this.generateInitialPackets()
      this.gameLoop() // 启动游戏循环，但红包在倒计时期间不会移动
      this.startCountdown()
    },

    // 开始倒计时
    startCountdown() {
      this.isCountingDown = true
      this.gameStartCountdown = 3

      const countdownInterval = setInterval(() => {
        this.gameStartCountdown--

        if (this.gameStartCountdown <= 0) {
          clearInterval(countdownInterval)
          this.isCountingDown = false
          this.startPacketsFalling()
        }
      }, 1000)
    },

    // 开始红包下落
    startPacketsFalling() {
      // 让所有静态红包开始下落
      this.redPackets.forEach(packet => {
        if (packet.isStatic) {
          const fallDuration = Math.max(3000 - this.gameConfig.gameSpeed * 100, 1500)
          packet.speed = this.gameConfig.gameHeight / (fallDuration / 16.67)
          packet.fallDuration = fallDuration
          packet.isStatic = false

          // 设置红包掉落完成后移除
          setTimeout(() => {
            const index = this.redPackets.findIndex(p => p.id === packet.id)
            if (index !== -1) {
              this.redPackets.splice(index, 1)
              // 如果没有红包被接住，游戏结束
              if (this.redPackets.length === 0 && this.gameRunning) {
                this.endGame()
              }
            }
          }, fallDuration)
        }
      })

      // 开始后续红包生成
      this.spawnRedPackets()
    },

    // 重新开始游戏
    restartGame() {
      this.playerPosition = this.gameConfig.gameWidth / 2 - this.gameConfig.playerWidth / 2
      this.startGame()
    },

    // 游戏主循环
    gameLoop() {
      if (!this.gameRunning) return

      this.updatePlayer()
      this.updateRedPackets()
      this.checkCollisions()

      this.gameLoopId = requestAnimationFrame(this.gameLoop)
    },

    // 更新玩家位置
    updatePlayer() {
      // 如果正在拖拽，则不使用键盘控制
      if (this.dragState.isDragging) {
        return
      }

      const moveSpeed = 0.05 // rem单位的移动速度

      if (this.keys.left && this.playerPosition > 0) {
        this.playerPosition -= moveSpeed
      }
      if (this.keys.right && this.playerPosition < this.gameConfig.gameWidth - this.gameConfig.playerWidth) {
        this.playerPosition += moveSpeed
      }
    },

    // 生成初始红包（游戏开始时）
    generateInitialPackets() {
      // 生成3-5个随机位置的红包
      const packetCount = 3 + Math.floor(Math.random() * 3) // 3-5个红包

      for (let i = 0; i < packetCount; i++) {
        const x = Math.random() * (this.gameConfig.gameWidth - 1)
        const y = Math.random() * (this.gameConfig.gameHeight * 0.6) // 在上半部分随机分布

        const packet = {
          id: this.packetIdCounter++,
          x: x,
          y: y,
          speed: 0, // 初始时不移动
          fallDuration: 0,
          isStatic: true // 标记为静态红包
        }

        this.redPackets.push(packet)
      }

      // 确保有一个红包在精灵上方很近的位置
      const guaranteedPacket = {
        id: this.packetIdCounter++,
        x: this.playerPosition + (this.gameConfig.playerWidth - 1) / 2, // 精灵中心位置
        y: this.gameConfig.gameHeight - 3, // 距离精灵很近的位置
        speed: 0,
        fallDuration: 0,
        isStatic: true
      }

      this.redPackets.push(guaranteedPacket)
      this.initialPacketsGenerated = true
    },

    // 生成红包（游戏进行中）
    spawnRedPackets() {
      if (!this.gameRunning || this.isCountingDown) return

      this.createRedPacket()

      // 随机间隔生成红包，游戏速度越快间隔越短
      const spawnDelay = Math.max(800 - this.gameConfig.gameSpeed * 50, 300)
      this.spawnTimeoutId = setTimeout(() => this.spawnRedPackets(), spawnDelay + Math.random() * 500)
    },

    // 创建红包
    createRedPacket() {
      const x = Math.random() * (this.gameConfig.gameWidth - 1) // 1rem为红包宽度
      const fallDuration = Math.max(3000 - this.gameConfig.gameSpeed * 100, 1500)

      const packet = {
        id: this.packetIdCounter++,
        x: x,
        y: -1.2, // rem单位，调整初始位置
        speed: this.gameConfig.gameHeight / (fallDuration / 16.67),
        fallDuration: fallDuration
      }

      this.redPackets.push(packet)

      // 红包掉落完成后移除
      setTimeout(() => {
        const index = this.redPackets.findIndex(p => p.id === packet.id)
        if (index !== -1) {
          this.redPackets.splice(index, 1)
          this.endGame()
        }
      }, fallDuration)
    },

    // 更新红包位置
    updateRedPackets() {
      this.redPackets.forEach(packet => {
        if (!packet.isStatic) {
          packet.y += packet.speed
        }
      })
    },

    // 检查碰撞
    checkCollisions() {
      for (let i = this.redPackets.length - 1; i >= 0; i--) {
        const packet = this.redPackets[i]
        const packetRect = {
          x: packet.x,
          y: packet.y,
          width: 1, // rem单位
          height: 1 // rem单位
        }

        const playerRect = {
          x: this.playerPosition,
          y: this.gameConfig.gameHeight - 1, // rem单位
          width: this.gameConfig.playerWidth,
          height: this.gameConfig.playerHeight
        }

        if (this.isColliding(packetRect, playerRect)) {
          this.collectRedPacket(packet, i)
        }
      }
    },

    // 碰撞检测
    isColliding(rect1, rect2) {
      return rect1.x < rect2.x + rect2.width &&
             rect1.x + rect1.width > rect2.x &&
             rect1.y < rect2.y + rect2.height &&
             rect1.y + rect1.height > rect2.y
    },

    // 收集红包
    collectRedPacket(packet, index) {
      this.redPackets.splice(index, 1)
      this.score += 10
      this.gameConfig.gameSpeed += 0.1

      // 添加收集效果
      this.showCollectEffect(packet.x, packet.y)
    },

    // 显示收集效果
    showCollectEffect(x, y) {
      const effect = {
        id: this.effectIdCounter++,
        x: x,
        y: y,
        points: 10
      }

      this.collectEffects.push(effect)

      setTimeout(() => {
        const index = this.collectEffects.findIndex(e => e.id === effect.id)
        if (index !== -1) {
          this.collectEffects.splice(index, 1)
        }
      }, 1000)
    },

    // 结束游戏
    endGame() {
      this.gameRunning = false
      this.gameOver = true

      if (this.gameLoopId) {
        cancelAnimationFrame(this.gameLoopId)
      }
      if (this.spawnTimeoutId) {
        clearTimeout(this.spawnTimeoutId)
      }

      this.redPackets.splice(0)
    }
  },

  mounted() {
    // 初始化rem适配
    this.initRemAdaptation()

    // 初始化游戏
    this.initGame()

    // 添加事件监听器
    document.addEventListener('keydown', this.handleKeyDown)
    document.addEventListener('keyup', this.handleKeyUp)
    document.addEventListener('mousemove', this.handleMouseMove)
    document.addEventListener('mouseup', this.handleMouseUp)

    // 添加全局触摸事件监听器（用于拖拽）
    document.addEventListener('touchmove', this.handleTouchMove)
    document.addEventListener('touchend', this.handleTouchEnd)
  },

  beforeUnmount() {
    // 清理事件监听器
    document.removeEventListener('keydown', this.handleKeyDown)
    document.removeEventListener('keyup', this.handleKeyUp)
    document.removeEventListener('mousemove', this.handleMouseMove)
    document.removeEventListener('mouseup', this.handleMouseUp)
    document.removeEventListener('touchmove', this.handleTouchMove)
    document.removeEventListener('touchend', this.handleTouchEnd)

    if (this.gameLoopId) {
      cancelAnimationFrame(this.gameLoopId)
    }
    if (this.spawnTimeoutId) {
      clearTimeout(this.spawnTimeoutId)
    }
  }
}
</script>

<style scoped>
.game-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Arial', sans-serif;
}

.game-area {
  position: relative;
  height: 9.28rem;
  width: 7rem;
  background: linear-gradient(to bottom, #87CEEB 0%, #98FB98 100%);
  border: 0.05rem solid #ffc107;
  border-radius: 0.2rem;
  overflow: hidden;
}

.player {
  position: absolute;
  bottom: 0.2rem;
  z-index: 10;
  transition: left 0.1s ease;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
  width: 1.6rem;
  height: 1.6rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: grab;
  user-select: none;
  touch-action: none; /* 防止移动端滚动 */
}

.player img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  pointer-events: none; /* 防止图片本身响应事件 */
}

/* 拖拽时禁用过渡效果，提高响应速度 */
.player.dragging {
  transition: none !important;
  cursor: grabbing;
  transform: scale(1.05); /* 拖拽时稍微放大 */
}

.player:active {
  cursor: grabbing;
}

.red-packet {
  position: absolute;
  font-size: 4em;
  z-index: 5;
  animation: fall linear;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
  width: 1rem;
  height: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

@keyframes fall {
  from {
    top: -1.2rem;
  }
  to {
    top: 100%;
  }
}

.instructions, .game-over, .countdown {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.95);
  padding: 0.2rem;
  border-radius: 0.15rem;
  text-align: center;
  box-shadow: 0 0.1rem 0.2rem rgba(0, 0, 0, 0.2);
  z-index: 20;
}

.countdown {
  background: rgba(255, 193, 7, 0.95);
  color: #fff;
  font-weight: bold;
}

.countdown h2 {
  font-size: 3em;
  margin: 0.1rem 0;
  color: #fff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.instructions p, .game-over p {
  margin-bottom: 0.2rem;
  color: #495057;
}

.instructions h2, .game-over h2 {
  color: #d63384;
  margin-bottom: 0.2rem;
  font-size: 2em;
}

.score {
  position: absolute;
  top: 0.2rem;
  left: 0.2rem;
  background: rgba(255, 255, 255, 0.9);
  padding: 0.1rem 0.15rem;
  border-radius: 0.1rem;
  font-weight: bold;
  color: #495057;
  z-index: 15;
  box-shadow: 0 0.02rem 0.1rem rgba(0, 0, 0, 0.1);
}

.collect-effect {
  position: absolute;
  color: #ff6b6b;
  font-weight: bold;
  font-size: 1.5em;
  z-index: 15;
  pointer-events: none;
  animation: fadeUp 1s ease-out forwards;
}

@keyframes fadeUp {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(-0.3rem);
  }
}

button {
  background: linear-gradient(45deg, #ff6b6b, #feca57);
  color: white;
  border: none;
  padding: 0.3rem 0.6rem;
  border-radius: 0.3rem;
  cursor: pointer;
  transition: transform 0.2s ease;
  font-weight: bold;
  box-shadow: 0 0.04rem 0.08rem rgba(0, 0, 0, 0.2);
  font-size: 1.2em;
  min-height: 0.8rem;
  min-width: 2rem;
  touch-action: manipulation; /* 防止移动端双击缩放 */
}

button:hover {
  transform: translateY(-0.02rem);
  box-shadow: 0 0.06rem 0.12rem rgba(0, 0, 0, 0.3);
}

button:active {
  transform: translateY(0);
  background: linear-gradient(45deg, #e55a5a, #e6b84f);
}
</style>
